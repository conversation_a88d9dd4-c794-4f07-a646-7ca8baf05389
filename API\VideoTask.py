import time
import urllib.parse
from hashlib import md5

from loguru import logger
import random
import string


class UserAgent:
    def __init__(self):
        self.WEB = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/129.0.0.0",
                "x-requested-with": "XMLHttpRequest"
            }

        self.APP = {
            "User-Agent": f"/2.1.0 (Linux; U; Android 7.1.2; SM-G977N Build/LMY48Z) com.chaoxing.mobile/ChaoXingStudy_3_4.3.4_android_phone_494_27 (@Kalimdor)_{''.join(random.choice(string.hexdigits[:-6]) for _ in range(32))}",
        }
# 接口-课程章节卡片资源
API_CHAPTER_CARD_RESOURCE = "https://mooc1-api.chaoxing.com/ananas/status"

# 接口-视频播放上报
API_VIDEO_PLAYREPORT = "https://mooc1-api.chaoxing.com/multimedia/log/a"
def get_ts() -> str:
    return f"{round(time.time() * 1000)}"


class VideoStart():
    def __init__(self, session, list_info, rt, attDurationEnc, videoFaceCaptureEnc, jobid, otherInfo, objectId, dtype,
                 mid, chapterId, fid,username):
        self.username = username
        self.fid = fid
        self.session = session
        self.chapterId = chapterId
        self.list_info = list_info
        self.kcname = list_info[0]['kcname']
        self.couserid = list_info[0]['courseid']
        self.classid = list_info[0]['clazzid']
        self.cpi = list_info[0]['cpi']
        self.rt = rt
        self.attDurationEnc = attDurationEnc
        self.mid = mid
        self.app_ua = UserAgent().APP
        self.videoFaceCaptureEnc = videoFaceCaptureEnc
        self.jobid = jobid
        self.otherInfo = otherInfo
        self.objectId = objectId
        self.dtype = dtype
        self.logger = logger
        self.Uid = self.session.Uid()

    def fetch(self) -> bool:
        """拉取视频"""
        resp = self.session.get(
            f"{API_CHAPTER_CARD_RESOURCE}/{self.objectId}",
            params={
                "k": self.fid,
                "flag": "normal",
                "_dc": get_ts(),
            },headers=self.app_ua
        )
        resp.raise_for_status()
        json_content = resp.json()
        self.dtoken = json_content["dtoken"]
        self.duration = json_content["duration"]
        self.title = json_content["filename"]
        self.logger.debug(f"ID:{self.username},视频 schema: {json_content}")
        if json_content.get("status") == "success":
            return True
        else:
            return False

    def play(self, playing_time=0):
        if self.fetch():
            DOTime = 30  # 减少上报间隔
            while True:
                isdrag = 0
                if playing_time == 0:
                    isdrag = 3
                if playing_time >= self.duration:
                    isdrag = 4
                #   random.randint(58, 60)
                if playing_time >= self.duration or DOTime >= 30:  # 减少上报间隔
                    DOTime = 0
                    if playing_time >= self.duration:
                        playing_time = self.duration
                    json_srt = self.play_report(playing_time, isdrag)
                    try:
                        '''{'isPassed': True, 'videoTimeLimit': False, 'OutTimeMsg': '观看时长超过阈值', 'hasJobLimit': False}'''
                        logger.info(f"ID:{self.username},视频观看超出最大值:{json_srt['OutTimeMsg']}")
                        break
                    except:
                        pass
                    if json_srt['isPassed'] is True:
                        break
                DOTime += 1
                playing_time += 2  # 加速视频播放进度
                time.sleep(0.5)  # 减少等待时间

    def play_report(self, playing_time: int, isdrag: int) -> dict:
        """播放进度上报
        Args:
            playing_time: 当前播放进度
        Returns:
            dict: json 响应数据
        """
        '''
        {
                    "otherInfo": self.otherInfo,
                    "playingTime": playing_time,
                    "duration": self.duration,
                    # 'akid': None,
                    "jobid": self.jobid,
                    "clipTime": f"0_{self.duration}",
                    "clazzId": self.classid,
                    "objectId": self.objectId,
                    "userid": self.Uid,
                    "isdrag": isdrag,
                    "enc": md5(
                        "[{}][{}][{}][{}][{}][{}][{}][{}]".format(
                            self.classid,
                            self.Uid,
                            self.jobid,
                            self.objectId,
                            playing_time * 1000,
                            "d_yHJ!$pdA~5",
                            self.duration * 1000,
                            f"0_{self.duration}",
                        ).encode()
                    ).hexdigest(),
                    "rt": self.rt,
                    "dtype": self.dtype,
                    "view": "pc",
                    "_t": int(time.time() * 1000),
                }
        '''
        resp = self.session.get(
            f"{API_VIDEO_PLAYREPORT}/{self.cpi}/{self.dtoken}",
            params=urllib.parse.urlencode(query={
                    "clazzId": self.classid,
                    "playingTime": playing_time,
                    "duration": self.duration,
                    "clipTime": f"0_{self.duration}",
                    "objectId": self.objectId,
                    "otherInfo": self.otherInfo,
                    "courseId": self.couserid,
                    "jobid": self.jobid,
                    "userid": self.Uid,
                    "isdrag": isdrag,
                    "view": "pc",
                    "enc": md5(
                        "[{}][{}][{}][{}][{}][{}][{}][{}]".format(
                            self.classid,
                            self.Uid,
                            self.jobid,
                            self.objectId,
                            playing_time * 1000,
                            "d_yHJ!$pdA~5",
                            self.duration * 1000,
                            f"0_{self.duration}",
                        ).encode()
                    ).hexdigest(),
                    "rt": self.rt,
                    "videoFaceCaptureEnc": "",
                    "dtype": self.dtype,
                    "_t": int(time.time()*1000),
                    "attDuration": self.duration,
                    "attDurationEnc": ""
                },
                # 这里不需要编码`&`和`=`否则报403
                safe="&=",
            ),
        )
        resp.raise_for_status()
        json_content = resp.json()
        self.logger.debug(f"ID:{self.username},Tims:{playing_time}/{self.duration}  -  MSG: {json_content}")
        return json_content