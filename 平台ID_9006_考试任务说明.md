# 平台ID 9006 考试任务处理说明

## 概述

本文档详细说明了为学习通跑单启动文件添加的平台ID 9006考试任务专门处理功能。

## 功能特性

### 1. 平台ID识别
- 系统会自动识别订单中的平台ID (cid)
- 当检测到 `cid = 9006` 时，系统将启用专门的考试任务处理逻辑
- 其他平台ID继续使用原有的课程学习逻辑

### 2. 考试任务处理流程

#### 2.1 登录验证
```python
# 使用MainXxt类进行登录
MyXxt = MainXxt(session, school, username, password, courseid, oid, cid, pool)
login_result = MyXxt.fanyalogin()
```

#### 2.2 课程信息获取
```python
# 获取课程列表和相关信息
course_result = MyXxt.kclist()
```

#### 2.3 考试处理
```python
# 使用EXAM类专门处理考试任务
exam_processor = EXAM(session, username, MyXxt.KcList, open=1)
exam_success = exam_processor.get_data()
```

### 3. 考试链接拼接机制

系统会根据课程信息自动拼接考试入口链接：

```
https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?courseId={courseid}&classId={clazzid}&examId={examtid}&cpi={cpi}
```

**参数说明：**
- `courseid`: 课程ID（如：253104021）
- `clazzid`: 班级ID
- `examtid`: 考试ID
- `cpi`: 课程实例ID

### 4. 滑块验证处理

系统集成了自动滑块验证功能：
- 使用ddddocr进行滑块距离计算
- 自动完成滑块验证码
- 获取验证成功后的validate值

### 5. 自动答题功能

- 从题库中匹配答案
- 支持多种题型（单选、多选、判断题等）
- 自动提交答案

## 数据库配置

### 订单查询更新
原SQL查询：
```sql
SELECT * FROM qingka_wangke_order WHERE cid IN (5393,5385,5383,5500) AND (status = '待处理' or status = '补刷中') ORDER BY oid
```

更新后SQL查询：
```sql
SELECT * FROM qingka_wangke_order WHERE cid IN (5393,5385,5383,5500,9006) AND (status = '待处理' or status = '补刷中') ORDER BY oid
```

### 数据洗涤更新
原SQL：
```sql
UPDATE qingka_wangke_order
SET status = '待处理', process = '', remarks = ''
WHERE cid IN (5393,5385,5383,5500)
AND status IN ('进行中', '等待中', '排队中', '停止中','休息中')
```

更新后SQL：
```sql
UPDATE qingka_wangke_order
SET status = '待处理', process = '', remarks = ''
WHERE cid IN (5393,5385,5383,5500,9006)
AND status IN ('进行中', '等待中', '排队中', '停止中','休息中')
```

## 状态更新机制

### 考试任务状态流程
1. **登录中**: `【考试任务】正在登录...` (0%)
2. **登录成功**: `【考试任务】登录成功，获取课程信息...` (20%)
3. **课程信息获取**: `【考试任务】课程信息获取成功，开始处理考试...` (50%)
4. **考试完成**: `【考试任务】考试处理完成` (100%)
5. **无考试**: `【考试任务】暂无待处理考试` (100%)

### 异常状态
- **登录失败**: `【考试任务】登录失败: {错误信息}`
- **课程匹配失败**: `【考试任务】课程信息匹配失败`
- **处理异常**: `【考试任务】处理异常: {异常信息}`

## 使用方法

### 1. 数据库订单配置
在 `qingka_wangke_order` 表中创建订单时，设置：
```sql
INSERT INTO qingka_wangke_order (cid, user, pass, school, kcid, status) 
VALUES (9006, '用户名', '密码', '学校名', '课程ID', '待处理');
```

### 2. 启动系统
运行学习通跑单启动文件：
```bash
python 学习通跑单启动文件.py
```

### 3. 监控日志
系统会输出详细的处理日志：
```
ID:用户名,检测到考试任务平台(CID:9006)，开始处理考试
ID:用户名,考试任务登录成功
ID:用户名,考试任务课程信息匹配成功
ID:用户名,考试任务处理完成
```

## 测试验证

运行测试脚本验证功能：
```bash
python test_exam_platform_9006.py
```

测试内容包括：
- 平台ID 9006识别测试
- 考试链接生成测试
- 处理流程验证

## 注意事项

1. **兼容性保证**: 新增功能不影响现有平台ID的处理逻辑
2. **错误处理**: 完整的异常捕获和错误日志记录
3. **状态追踪**: 详细的进度更新和状态反馈
4. **安全性**: 保持原有的登录验证和安全机制

## 技术架构

```
学习通跑单启动文件.py
├── order_get() - 订单获取（包含9006）
├── Run() - 主处理函数
│   ├── cid == 9006 判断
│   └── handle_exam_task() - 考试任务处理
│       ├── MainXxt.fanyalogin() - 登录
│       ├── MainXxt.kclist() - 课程信息
│       └── EXAM.get_data() - 考试处理
└── data/Exam.py - 考试核心逻辑
    ├── get_data() - 获取考试数据
    ├── Captcha() - 滑块验证
    ├── OpenExam() - 进入考试
    └── Do() - 自动答题
```

## 更新日志

- **v1.0**: 初始版本，添加平台ID 9006考试任务支持
- 支持自动考试链接拼接
- 集成滑块验证处理
- 完整的状态追踪和错误处理
